/* Custom Scrollbar Styles */
.custom-scrollbar {
  /* Firefox */
  scrollbar-width: thin !important;
  scrollbar-color: rgba(59, 130, 246, 0.3) transparent !important;
}

/* Webkit browsers (Chrome, Safari, Edge) */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px !important;
  display: block !important;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.2) 0%,
    rgba(147, 51, 234, 0.2) 100%
  );
  border-radius: 3px;
  border: none;
  transition: all 0.3s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.4) 0%,
    rgba(147, 51, 234, 0.4) 100%
  );
}

.custom-scrollbar::-webkit-scrollbar-thumb:active {
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 1) 0%,
    rgba(147, 51, 234, 1) 100%
  );
}

/* Corner where horizontal and vertical scrollbars meet */
.custom-scrollbar::-webkit-scrollbar-corner {
  background: transparent;
}

/* Smooth scrolling behavior */
.custom-scrollbar {
  scroll-behavior: smooth;
}

/* Hide scrollbar completely but keep functionality */
.no-scrollbar {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.no-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* Hide scrollbar on mobile but keep functionality */
@media (max-width: 768px) {
  .custom-scrollbar {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }

  .custom-scrollbar::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

/* Neumorphic scrollbar variant */
.custom-scrollbar-neumorphic::-webkit-scrollbar-thumb {
  background: linear-gradient(
    135deg,
    rgba(30, 41, 59, 0.8) 0%,
    rgba(51, 65, 85, 0.8) 100%
  );
  border-radius: 4px;
  border: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow:
    inset 2px 2px 4px rgba(0, 0, 0, 0.3),
    inset -2px -2px 4px rgba(255, 255, 255, 0.1);
}

.custom-scrollbar-neumorphic::-webkit-scrollbar-thumb:hover {
  border-color: rgba(59, 130, 246, 0.5);
  box-shadow:
    inset 2px 2px 4px rgba(0, 0, 0, 0.4),
    inset -2px -2px 4px rgba(255, 255, 255, 0.15),
    0 0 8px rgba(59, 130, 246, 0.3);
}
