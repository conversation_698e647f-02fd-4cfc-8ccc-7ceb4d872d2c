import { useLocation, useNavigate } from "react-router-dom";
import { useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";

const NotFound = () => {
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-black text-white">
      <div className="text-center">
        <div className="mb-8">
          <h1 className="text-8xl font-black mb-4 bg-gradient-to-r from-white to-vital-lime bg-clip-text text-transparent">
            404
          </h1>
          <h2 className="text-2xl font-bold text-vital-lime mb-4">Page Not Found</h2>
          <p className="text-xl text-gray-300 mb-8">
            The page you're looking for doesn't exist or has been moved.
          </p>
        </div>

        <div className="space-y-4">
          <Button
            onClick={() => navigate('/')}
            className="glow-button bg-vital-lime text-black hover:bg-vital-lime-bright font-bold text-lg px-8 py-3 rounded-full transition-all duration-300 hover:scale-105 mr-4"
          >
            Return Home
          </Button>
          <Button
            onClick={() => navigate('/dashboard')}
            variant="outline"
            className="border-vital-lime text-vital-lime hover:bg-vital-lime hover:text-black font-bold text-lg px-8 py-3 rounded-full transition-all duration-300"
          >
            Go to Dashboard
          </Button>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
