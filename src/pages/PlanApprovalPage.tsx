import { useNavigate } from 'react-router-dom';
import PlanApprovalComponent from '@/components/PlanApproval';

const PlanApprovalPage = () => {
  const navigate = useNavigate();

  const handleNavigate = (page: string) => {
    switch (page) {
      case 'generate':
        navigate('/generate-plan');
        break;
      case 'dashboard':
        navigate('/dashboard');
        break;
      case 'landing':
        navigate('/');
        break;
      default:
        navigate('/');
    }
  };

  return <PlanApprovalComponent onNavigate={handleNavigate} />;
};

export default PlanApprovalPage;
