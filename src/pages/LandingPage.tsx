import { useNavigate } from 'react-router-dom';
import LandingPageComponent from '@/components/LandingPage';

const LandingPage = () => {
  const navigate = useNavigate();

  const handleNavigate = (page: string) => {
    switch (page) {
      case 'generate':
        navigate('/generate-plan');
        break;
      case 'dashboard':
        navigate('/dashboard');
        break;
      default:
        navigate('/');
    }
  };

  return <LandingPageComponent onNavigate={handleNavigate} />;
};

export default LandingPage;
