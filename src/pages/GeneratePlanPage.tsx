import { useNavigate } from 'react-router-dom';
import GeneratePlanComponent from '@/components/GeneratePlan';

const GeneratePlanPage = () => {
  const navigate = useNavigate();

  const handleNavigate = (page: string) => {
    switch (page) {
      case 'landing':
        navigate('/');
        break;
      case 'approval':
        navigate('/plan-approval');
        break;
      case 'dashboard':
        navigate('/dashboard');
        break;
      default:
        navigate('/');
    }
  };

  return <GeneratePlanComponent onNavigate={handleNavigate} />;
};

export default GeneratePlanPage;
