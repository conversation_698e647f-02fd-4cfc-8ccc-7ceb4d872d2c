
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  @layer base {
    :root {
      /* === Core Backgrounds === */
      --background: 230 45% 6%;             /* Deep blue-black base */
      --foreground: 0 0% 100%;              /* Bright white for text */

      --card: 230 40% 10%;                  /* Slightly lighter than bg for cards */
      --card-foreground: 220 20% 98%;

      --popover: 230 45% 12%;
      --popover-foreground: 220 20% 98%;

      /* === Primary: Neon Blue Glow === */
      --primary: 210 100% 60%;              /* Vibrant neon blue */
      --primary-foreground: 0 0% 10%;

      /* === Accent: Purple for Graphs/Highlights === */
      --accent: 270 100% 65%;               /* Vibrant neon purple */
      --accent-foreground: 0 0% 100%;

      /* === Sidebar and Secondary Elements === */
      --secondary: 230 35% 12%;
      --secondary-foreground: 0 0% 100%;

      --muted: 230 20% 18%;
      --muted-foreground: 220 10% 60%;

      --border: 230 20% 20%;
      --input: 230 25% 15%;
      --ring: 210 100% 70%;                 /* Glow effect */

      --destructive: 0 80% 60%;
      --destructive-foreground: 0 0% 100%;

      --radius: 1rem;

      /* === Custom Theme Enhancements === */
      --glass-blur: blur(12px);
      --shadow-elevated: 0 8px 24px rgba(0, 0, 0, 0.25);
      --shadow-neumorphic: 6px 6px 12px rgba(0, 0, 0, 0.25), -6px -6px 12px rgba(35, 40, 60, 0.15);
      --shadow-inner-neumorphic: inset 2px 2px 5px rgba(0, 0, 0, 0.2), inset -2px -2px 5px rgba(35, 40, 60, 0.1);
      --glow-blue: 0 0 15px rgba(0, 140, 255, 0.3);
      --glow-purple: 0 0 15px rgba(149, 0, 255, 0.3);
      --glass-blur: blur(16px);
      --gradient-blue: linear-gradient(135deg, hsl(210, 100%, 60%), hsl(220, 100%, 45%));
      --gradient-purple: linear-gradient(135deg, hsl(270, 100%, 65%), hsl(280, 100%, 45%));
      --gradient-background: linear-gradient(135deg, hsl(230, 45%, 10%), hsl(240, 50%, 3%));
      --glass-background: rgba(20, 25, 40, 0.7);
    }
  }

}


@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter;
    background: var(--gradient-background);
    overflow-x: hidden; /* Hide horizontal scrollbar only */
  }

  /* Universal Scrolling System - Enhanced */
  .app-scrollable {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    scrollbar-width: thin !important;
    scrollbar-color: rgba(59, 130, 246, 0.5) rgba(25, 30, 45, 0.3) !important;
    scroll-behavior: smooth !important;
    max-height: 100vh !important;
  }

  .app-scrollable::-webkit-scrollbar {
    width: 8px !important;
    display: block !important;
  }

  .app-scrollable::-webkit-scrollbar-track {
    background: rgba(25, 30, 45, 0.3) !important;
    border-radius: 4px !important;
    border: 1px solid rgba(59, 130, 246, 0.1) !important;
  }

  .app-scrollable::-webkit-scrollbar-thumb {
    background: linear-gradient(
      135deg,
      rgba(59, 130, 246, 0.6) 0%,
      rgba(147, 51, 234, 0.6) 100%
    ) !important;
    border-radius: 4px !important;
    border: 1px solid rgba(59, 130, 246, 0.2) !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 0 4px rgba(59, 130, 246, 0.3) !important;
  }

  .app-scrollable::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(
      135deg,
      rgba(59, 130, 246, 0.8) 0%,
      rgba(147, 51, 234, 0.8) 100%
    ) !important;
    box-shadow: 0 0 8px rgba(59, 130, 246, 0.5) !important;
  }

  .app-scrollable::-webkit-scrollbar-corner {
    background: rgba(25, 30, 45, 0.3) !important;
  }

  /* Neumorphic Card Styles */
  .card-neumorphic {
    @apply rounded-xl relative overflow-hidden;
    box-shadow: var(--shadow-neumorphic);
    background: linear-gradient(145deg, rgba(25, 30, 45, 0.6), rgba(15, 20, 35, 0.8));
    border: 1px solid rgba(255, 255, 255, 0.05);
  }

  /* Glassmorphic Card Styles */
  .card-glass {
    @apply rounded-xl relative overflow-hidden;
    background: var(--glass-background);
    backdrop-filter: var(--glass-blur);
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: var(--shadow-elevated);
  }

  /* Glowing Button Styles */
  .btn-glow-blue {
    @apply relative overflow-hidden transition-all duration-300;
    background: var(--gradient-blue);
    box-shadow: var(--glow-blue);
  }

  .btn-glow-blue:hover {
    box-shadow: 0 0 8px rgba(0, 140, 255, 0.3);
    transform: translateY(-1px);
  }

  .btn-glow-purple {
    @apply relative overflow-hidden transition-all duration-300;
    background: var(--gradient-purple);
    box-shadow: var(--glow-purple);
  }

  .btn-glow-purple:hover {
    box-shadow: 0 0 8px rgba(149, 0, 255, 0.3);
    transform: translateY(-1px);
  }

  /* Glowing Border Effect */
  .border-glow-blue {
    border: 1px solid rgba(0, 140, 255, 0.2);
    box-shadow: 0 0 3px rgba(0, 140, 255, 0.1);
  }

  .border-glow-purple {
    border: 1px solid rgba(149, 0, 255, 0.2);
    box-shadow: 0 0 3px rgba(149, 0, 255, 0.1);
  }

  /* Focus States */
  .focus-glow:focus {
    @apply outline-none;
    box-shadow: 0 0 0 1px rgba(0, 140, 255, 0.3), 0 0 5px rgba(0, 140, 255, 0.1);
  }

  /* Shadow Glow Effects */
  .shadow-glow-blue {
    box-shadow: var(--glow-blue);
  }

  .shadow-glow-purple {
    box-shadow: var(--glow-purple);
  }
}
