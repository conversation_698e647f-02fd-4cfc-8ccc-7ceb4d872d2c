interface UserData {
  calories: number;
  waterIntake: number;
  streak: number;
  xp: number;
  level: number;
  dailyGoal: number;
  waterGoal: number;
}



class AIService {
  private apiKey: string;
  private baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent';
  private streamUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:streamGenerateContent';
  private enableStreaming: boolean;
  private enableMarkdown: boolean;

  constructor() {
    this.apiKey = import.meta.env.VITE_GEMINI_API_KEY || '';
    this.enableStreaming = import.meta.env.VITE_ENABLE_STREAMING === 'true';
    this.enableMarkdown = import.meta.env.VITE_ENABLE_MARKDOWN === 'true';

    console.log('🔧 AI Service Debug Info:');
    console.log('- Environment variables available:', Object.keys(import.meta.env));
    console.log('- API Key found:', this.apiKey ? `Yes (${this.apiKey.substring(0, 10)}...)` : 'No');
    console.log('- Streaming enabled:', this.enableStreaming);
    console.log('- Markdown enabled:', this.enableMarkdown);
    console.log('- Base URL:', this.baseUrl);
    console.log('- Stream URL:', this.streamUrl);

    if (!this.apiKey) {
      console.warn('⚠️ Gemini API key not found. AI features will use fallback responses.');
      console.log('💡 To enable AI: Add VITE_GEMINI_API_KEY to your .env file');
    } else {
      console.log('✅ Gemini API key configured successfully');
    }
  }

  private getSystemPrompt(userData?: UserData): string {
    const userStats = userData ? `
Current User Stats:
- Calories consumed: ${userData.calories}/${userData.dailyGoal} (${Math.round((userData.calories / userData.dailyGoal) * 100)}%)
- Water intake: ${userData.waterIntake}L/${userData.waterGoal}L (${Math.round((userData.waterIntake / userData.waterGoal) * 100)}%)
- Current streak: ${userData.streak} days
- Experience points: ${userData.xp} XP
- Level: ${userData.level}
` : '';

    return `You are VitalEdge Assistant, an expert AI health and fitness coach integrated into the VitalEdge wellness platform. You provide personalized, actionable advice on fitness, nutrition, hydration, and overall wellness.

${userStats}

Your personality and approach:
- Encouraging and motivational, but realistic
- Evidence-based recommendations
- Personalized advice based on user's current progress
- Concise but comprehensive responses
- Use emojis sparingly but effectively
- Focus on sustainable habits over quick fixes

Key areas of expertise:
1. **Fitness & Exercise**: Workout planning, form correction, progression strategies
2. **Nutrition**: Meal planning, calorie management, macro/micronutrients
3. **Hydration**: Water intake optimization, timing recommendations
4. **Recovery**: Sleep optimization, rest day planning, stress management
5. **Goal Setting**: SMART goals, habit formation, progress tracking

Response guidelines:
- Keep responses under 200 words unless detailed explanation is needed
- Always consider the user's current stats when giving advice
- Provide actionable next steps
- Acknowledge their progress and streak when relevant
- If asked about medical conditions, recommend consulting healthcare professionals
- Stay within your expertise - fitness, nutrition, and wellness

Formatting guidelines:
- Use **bold** for important points and key terms
- Use bullet points (•) for lists and recommendations
- Use line breaks for better readability
- Structure responses with clear sections when appropriate
- Use emojis sparingly but effectively (💪, 🥗, 💧, 🔥, ⭐)
- Highlight numbers and stats with **bold**

Remember: You're not just answering questions, you're helping users build sustainable healthy habits and achieve their wellness goals. Format your responses to be visually appealing and easy to scan.`;
  }

  private getFallbackResponse(userMessage: string, userData?: UserData): string {
    const lowerCaseMessage = userMessage.toLowerCase();

    // Workout related questions
    if (lowerCaseMessage.includes("workout") || lowerCaseMessage.includes("exercise") || lowerCaseMessage.includes("training")) {
      if (lowerCaseMessage.includes("best") || lowerCaseMessage.includes("recommend")) {
        return `Based on your current progress, I recommend focusing on compound movements like squats, deadlifts, and push-ups. Your ${userData?.streak || 0}-day streak shows great consistency! 💪\n\nFor optimal results:\n• 3-4 strength sessions per week\n• 2-3 cardio sessions\n• Always include rest days\n\nWhat specific area would you like to focus on?`;
      } else if (lowerCaseMessage.includes("often") || lowerCaseMessage.includes("frequency")) {
        return `Great question! For sustainable progress, aim for 4-5 workout days per week. Your current ${userData?.streak || 0}-day streak is impressive! 🔥\n\nIdeal weekly split:\n• 3 strength training days\n• 2 cardio/conditioning days\n• 2 active recovery days\n\nQuality over quantity - consistency beats intensity every time!`;
      }
      return `Exercise is crucial for both physical and mental health. Your dedication shows with your current streak! Focus on progressive overload and proper form. What specific workout questions do you have?`;
    }

    // Nutrition related questions
    if (lowerCaseMessage.includes("food") || lowerCaseMessage.includes("diet") || lowerCaseMessage.includes("nutrition") || lowerCaseMessage.includes("eat")) {
      if (userData) {
        const calorieProgress = Math.round((userData.calories / userData.dailyGoal) * 100);
        return `Looking at your nutrition today - you're at ${userData.calories}/${userData.dailyGoal} calories (${calorieProgress}%). ${calorieProgress < 80 ? "You might need more fuel for your workouts!" : calorieProgress > 120 ? "You're a bit over your goal, but that's okay occasionally!" : "Great job staying on track!"} 🍎\n\nKey nutrition tips:\n• Prioritize protein (0.8-1g per lb bodyweight)\n• Include vegetables with every meal\n• Stay hydrated\n• Time carbs around workouts\n\nWhat specific nutrition goals are you working on?`;
      }
      return `Nutrition is 70% of your results! Focus on whole foods, adequate protein, and staying consistent. What specific nutrition questions do you have?`;
    }

    // Hydration related questions
    if (lowerCaseMessage.includes("water") || lowerCaseMessage.includes("hydration") || lowerCaseMessage.includes("drink")) {
      if (userData) {
        const hydrationProgress = Math.round((userData.waterIntake / userData.waterGoal) * 100);
        return `Your hydration today: ${userData.waterIntake}L/${userData.waterGoal}L (${hydrationProgress}%) 💧\n\n${hydrationProgress < 70 ? "Time to drink up! Try setting hourly reminders." : hydrationProgress > 100 ? "Excellent hydration! You're crushing your goals!" : "Good progress! Keep sipping throughout the day."}\n\nHydration tips:\n• Drink 16-20oz upon waking\n• Sip consistently vs. chugging\n• Add electrolytes for intense workouts\n• Monitor urine color as a guide`;
      }
      return `Proper hydration is essential for performance and recovery. Aim for at least 8 glasses daily, more if you're active. How's your water intake today?`;
    }

    // Progress and motivation
    if (lowerCaseMessage.includes("progress") || lowerCaseMessage.includes("motivation") || lowerCaseMessage.includes("streak")) {
      if (userData) {
        return `Your progress is fantastic! 🌟\n\n• ${userData.streak}-day streak - incredible consistency!\n• Level ${userData.level} with ${userData.xp} XP earned\n• Nutrition: ${Math.round((userData.calories / userData.dailyGoal) * 100)}% of daily goal\n• Hydration: ${Math.round((userData.waterIntake / userData.waterGoal) * 100)}% of target\n\nRemember: Progress isn't always linear. You're building lifelong habits, and that takes time. Every day you show up, you're winning! 🏆\n\nWhat's your biggest challenge right now?`;
      }
      return `Every day you choose health is a victory! Consistency beats perfection. What specific area would you like to improve?`;
    }

    // Default responses
    const defaultResponses = [
      `I'm here to help with your fitness and wellness journey! Your consistency is paying off. What specific area would you like to focus on today? 💪`,
      `Great to see you staying engaged with your health goals! Whether it's workouts, nutrition, or hydration - I'm here to help. What's on your mind? 🌟`,
      `Your dedication to wellness is inspiring! I can help with workout planning, nutrition advice, or any health-related questions. What would you like to explore? 🎯`,
      `Consistency is key, and you're demonstrating that beautifully! How can I support your wellness journey today? 🚀`
    ];

    return defaultResponses[Math.floor(Math.random() * defaultResponses.length)];
  }

  async generatePlan(formData: any): Promise<any> {
    console.log('🏋️ AI Service - generatePlan called');
    console.log('- Form data:', formData);

    // Create a comprehensive plan based on form data
    const nutritionPlan = this.generateNutritionPlan(formData);
    const lifestylePlan = this.generateLifestylePlan(formData);

    const plan = {
      workoutPlan: this.generateWorkoutPlan(formData),
      mealPlan: nutritionPlan,
      hydration: lifestylePlan.hydration,
      sleep: lifestylePlan.sleep,
      goals: this.generateGoals(formData)
    };

    console.log('✅ Plan generated successfully');
    return plan;
  }

  private generateWorkoutPlan(formData: any) {
    const workoutDays = parseInt(formData.workoutDays) || 3;
    const workoutHours = parseFloat(formData.workoutHours) || 1;
    const goal = formData.goal || 'general fitness';

    const workoutTypes = {
      'muscle-gain': ['Upper Body Strength', 'Lower Body Power', 'Push Day', 'Pull Day', 'Leg Day', 'Full Body Strength'],
      'weight-loss': ['HIIT Cardio', 'Circuit Training', 'Cardio & Core', 'Fat Burn Circuit', 'Metabolic Training'],
      'endurance': ['Cardio Endurance', 'Running', 'Cycling', 'Swimming', 'Aerobic Training'],
      'general-fitness': ['Full Body Workout', 'Functional Training', 'Mixed Training', 'Bodyweight Circuit']
    };

    const exercises = {
      'muscle-gain': ['Push-ups', 'Pull-ups', 'Squats', 'Deadlifts', 'Bench Press', 'Rows', 'Shoulder Press'],
      'weight-loss': ['Burpees', 'Mountain Climbers', 'Jump Squats', 'High Knees', 'Plank Jacks', 'Jumping Jacks'],
      'endurance': ['Running', 'Cycling', 'Swimming', 'Rowing', 'Elliptical', 'Stair Climbing'],
      'general-fitness': ['Squats', 'Push-ups', 'Lunges', 'Planks', 'Jumping Jacks', 'Burpees']
    };

    const workoutList = workoutTypes[goal] || workoutTypes['general-fitness'];
    const exerciseList = exercises[goal] || exercises['general-fitness'];

    const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
    const plan = [];

    for (let i = 0; i < workoutDays; i++) {
      const dayIndex = Math.floor((i * 7) / workoutDays);
      const workout = workoutList[i % workoutList.length];
      const dayExercises = exerciseList.slice(i * 2, (i * 2) + 4);

      plan.push({
        day: days[dayIndex],
        workout,
        duration: `${Math.floor(workoutHours * 60)} min`,
        exercises: dayExercises
      });
    }

    return plan;
  }

  private generateNutritionPlan(formData: any) {
    const goal = formData.goal || 'general fitness';
    const restrictions = formData.dietaryRestrictions || 'none';

    const mealPlans = {
      'muscle-gain': {
        breakfast: { meal: 'Protein Oatmeal with Berries', calories: 450, protein: '25g', carbs: '55g', fat: '12g' },
        lunch: { meal: 'Grilled Chicken & Quinoa Bowl', calories: 550, protein: '40g', carbs: '45g', fat: '18g' },
        dinner: { meal: 'Salmon with Sweet Potato', calories: 500, protein: '35g', carbs: '40g', fat: '20g' },
        snacks: [
          { meal: 'Greek Yogurt with Nuts', calories: 200, protein: '15g', carbs: '12g', fat: '10g' },
          { meal: 'Protein Shake', calories: 150, protein: '25g', carbs: '5g', fat: '3g' }
        ]
      },
      'weight-loss': {
        breakfast: { meal: 'Veggie Omelet', calories: 300, protein: '20g', carbs: '8g', fat: '18g' },
        lunch: { meal: 'Grilled Chicken Salad', calories: 350, protein: '30g', carbs: '15g', fat: '15g' },
        dinner: { meal: 'Baked Fish with Vegetables', calories: 400, protein: '35g', carbs: '20g', fat: '12g' },
        snacks: [
          { meal: 'Apple with Almond Butter', calories: 150, protein: '4g', carbs: '20g', fat: '8g' },
          { meal: 'Celery with Hummus', calories: 100, protein: '3g', carbs: '12g', fat: '4g' }
        ]
      }
    };

    return mealPlans[goal] || mealPlans['weight-loss'];
  }

  private generateLifestylePlan(formData: any) {
    const waterIntake = parseFloat(formData.waterIntake) || 2.5;
    const sleepHours = parseInt(formData.sleepHours) || 8;

    return {
      hydration: {
        goal: `${waterIntake}L`,
        schedule: ['Morning: 500ml', 'Pre-workout: 250ml', 'Post-workout: 500ml', 'Evening: 250ml'],
        tips: ['Drink water upon waking', 'Carry a water bottle', 'Set hourly reminders']
      },
      sleep: {
        target: `${sleepHours} hours`,
        bedtime: '10:30 PM',
        wakeup: '6:30 AM',
        tips: ['No screens 1 hour before bed', 'Keep room cool', 'Consistent schedule']
      }
    };
  }

  private generateGoals(formData: any) {
    const goal = formData.goal || 'general fitness';

    const goalMappings = {
      'muscle-gain': {
        primary: 'Build lean muscle mass',
        secondary: ['Increase strength', 'Improve body composition', 'Enhance recovery'],
        timeline: '12-16 weeks',
        metrics: ['Body weight', 'Muscle measurements', 'Strength benchmarks']
      },
      'weight-loss': {
        primary: 'Lose body fat sustainably',
        secondary: ['Improve cardiovascular health', 'Increase energy', 'Build healthy habits'],
        timeline: '8-12 weeks',
        metrics: ['Body weight', 'Body fat percentage', 'Waist circumference']
      },
      'endurance': {
        primary: 'Improve cardiovascular endurance',
        secondary: ['Increase stamina', 'Better heart health', 'Enhanced performance'],
        timeline: '6-10 weeks',
        metrics: ['Running distance', 'Heart rate recovery', 'VO2 max']
      }
    };

    return goalMappings[goal] || goalMappings['weight-loss'];
  }

  async generateResponse(userMessage: string, userData?: UserData, onStream?: (chunk: string) => void): Promise<string> {
    console.log('🤖 AI Service - generateResponse called');
    console.log('- User message:', userMessage);
    console.log('- User data provided:', userData ? 'Yes' : 'No');
    console.log('- API key available:', this.apiKey ? 'Yes' : 'No');
    console.log('- Streaming enabled:', this.enableStreaming);
    console.log('- Streaming callback provided:', onStream ? 'Yes' : 'No');

    // If no API key, use fallback
    if (!this.apiKey) {
      console.log('📝 Using fallback response (no API key)');
      const fallbackResponse = this.getFallbackResponse(userMessage, userData);
      console.log('- Fallback response:', fallbackResponse.substring(0, 100) + '...');

      // Simulate smooth streaming for fallback if callback provided
      if (onStream && this.enableStreaming) {
        console.log('🎭 Simulating smooth streaming for fallback response...');
        let currentText = '';
        const chars = fallbackResponse.split('');

        for (let i = 0; i < chars.length; i++) {
          currentText += chars[i];
          onStream(currentText);

          // Variable delay for more natural typing
          const char = chars[i];
          let delay = 15; // Base delay

          if (char === ' ') delay = 30; // Pause at spaces
          else if (char === '.' || char === '!' || char === '?') delay = 100; // Pause at sentence endings
          else if (char === ',' || char === ';') delay = 50; // Pause at commas
          else if (char === '\n') delay = 80; // Pause at line breaks

          await new Promise(resolve => setTimeout(resolve, delay));
        }
        console.log('✅ Fallback streaming simulation complete');
      }

      return fallbackResponse;
    }

    try {
      console.log('🚀 Making API request to Gemini...');

      // Combine system prompt with user message for Gemini
      const systemPrompt = this.getSystemPrompt(userData);
      const fullPrompt = `${systemPrompt}\n\nUser: ${userMessage}\n\nAssistant:`;

      console.log('- Full prompt length:', fullPrompt.length);
      console.log('- System prompt preview:', systemPrompt.substring(0, 200) + '...');

      const requestBody = {
        contents: [
          {
            parts: [
              {
                text: fullPrompt
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 300,
          stopSequences: []
        },
        safetySettings: [
          {
            category: "HARM_CATEGORY_HARASSMENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_HATE_SPEECH",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_DANGEROUS_CONTENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          }
        ]
      };

      console.log('- Request body prepared, making fetch request...');

      // Choose streaming or regular endpoint
      const useStreaming = this.enableStreaming && onStream;
      const requestUrl = useStreaming
        ? `${this.streamUrl}?key=${this.apiKey}&alt=sse`
        : `${this.baseUrl}?key=${this.apiKey}`;

      console.log('- Request URL:', requestUrl.replace(this.apiKey, 'API_KEY_HIDDEN'));
      console.log('- Using streaming:', useStreaming);

      const response = await fetch(requestUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      console.log('📡 API Response received:');
      console.log('- Status:', response.status);
      console.log('- Status text:', response.statusText);
      console.log('- Headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ Gemini API Error:', errorData);
        console.log('🔄 Falling back to local response...');
        return this.getFallbackResponse(userMessage, userData);
      }

      // Handle streaming response
      if (useStreaming && response.body) {
        console.log('🌊 Processing streaming response...');
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let fullText = '';

        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const chunk = decoder.decode(value, { stream: true });
            const lines = chunk.split('\n');

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                try {
                  const jsonData = JSON.parse(line.slice(6));
                  const text = jsonData.candidates?.[0]?.content?.parts?.[0]?.text;
                  if (text) {
                    fullText += text;
                    onStream?.(fullText);
                  }
                } catch (e) {
                  // Ignore parsing errors for incomplete chunks
                }
              }
            }
          }
        } finally {
          reader.releaseLock();
        }

        console.log('✅ Streaming complete, full text length:', fullText.length);
        return fullText.trim();
      }

      // Handle regular response
      const data = await response.json();
      console.log('📦 API Response data:', data);

      const generatedText = data.candidates?.[0]?.content?.parts?.[0]?.text;

      if (generatedText) {
        console.log('✅ Generated text received:', generatedText.substring(0, 100) + '...');
        return generatedText.trim();
      } else {
        console.warn('⚠️ No content generated from Gemini API, response structure:', JSON.stringify(data, null, 2));
        console.log('🔄 Falling back to local response...');
        return this.getFallbackResponse(userMessage, userData);
      }
    } catch (error) {
      console.error('💥 AI Service Error:', error);
      console.log('- Error type:', error instanceof Error ? error.constructor.name : typeof error);
      console.log('- Error message:', error instanceof Error ? error.message : String(error));
      console.log('🔄 Falling back to local response...');
      return this.getFallbackResponse(userMessage, userData);
    }
  }
}

export const aiService = new AIService();
export type { UserData };
