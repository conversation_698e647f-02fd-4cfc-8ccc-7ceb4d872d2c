
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Check, Trash2, Edit3, X, Save } from "lucide-react";
import { toast } from "@/hooks/use-toast";

export interface Task {
  id: string;
  title: string;
  completed: boolean;
  createdAt: string;
}

const TaskComponent = () => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [newTask, setNewTask] = useState("");
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editingText, setEditingText] = useState("");

  // Load tasks from localStorage on component mount
  useEffect(() => {
    const savedTasks = localStorage.getItem("vitalEdgeTasks");
    if (savedTasks) {
      setTasks(JSON.parse(savedTasks));
    }
  }, []);

  // Save tasks to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem("vitalEdgeTasks", JSON.stringify(tasks));
  }, [tasks]);

  const addTask = () => {
    if (newTask.trim() === "") {
      toast({
        title: "Task cannot be empty",
        variant: "destructive",
      });
      return;
    }

    const task: Task = {
      id: Date.now().toString(),
      title: newTask,
      completed: false,
      createdAt: new Date().toISOString(),
    };

    setTasks([...tasks, task]);
    setNewTask("");
    toast({
      title: "Task added",
      description: "Your task has been added successfully.",
    });
  };

  const toggleComplete = (id: string) => {
    setTasks(
      tasks.map((task) =>
        task.id === id ? { ...task, completed: !task.completed } : task
      )
    );
  };

  const deleteTask = (id: string) => {
    setTasks(tasks.filter((task) => task.id !== id));
    toast({
      title: "Task deleted",
      description: "Your task has been deleted.",
    });
  };

  const startEdit = (id: string, title: string) => {
    setEditingId(id);
    setEditingText(title);
  };

  const cancelEdit = () => {
    setEditingId(null);
    setEditingText("");
  };

  const saveEdit = () => {
    if (editingText.trim() === "") {
      toast({
        title: "Task cannot be empty",
        variant: "destructive",
      });
      return;
    }

    setTasks(
      tasks.map((task) =>
        task.id === editingId ? { ...task, title: editingText.trim() } : task
      )
    );

    setEditingId(null);
    setEditingText("");

    toast({
      title: "Task updated",
      description: "Your task has been updated successfully.",
    });
  };

  const clearCompleted = () => {
    const completedCount = tasks.filter(task => task.completed).length;
    if (completedCount === 0) {
      toast({
        title: "No completed tasks",
        description: "There are no completed tasks to clear.",
      });
      return;
    }

    setTasks(tasks.filter((task) => !task.completed));
    toast({
      title: "Completed tasks cleared",
      description: `${completedCount} completed task${completedCount > 1 ? 's' : ''} removed.`,
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-blue">Today's Tasks</h2>
        <button
          onClick={clearCompleted}
          className="bg-glass/50 border border-red-500/20 text-white px-3 py-2 rounded-lg hover:border-red-500/40 transition-all duration-300 text-sm"
        >
          🧹 Clear Completed
        </button>
      </div>

      <div className="card-neumorphic p-5">
        <div className="flex space-x-3 mb-6">
          <Input
            value={newTask}
            onChange={(e) => setNewTask(e.target.value)}
            placeholder="Add a new task..."
            className="bg-glass/50 backdrop-blur-sm border border-neon-blue/20 text-white placeholder:text-white/60 focus:border-neon-blue/40 focus:shadow-glow-blue outline-none transition-all duration-300"
            onKeyDown={(e) => e.key === "Enter" && addTask()}
          />
          <Button
            onClick={addTask}
            className="btn-glow-blue text-white px-6"
          >
            Add Task
          </Button>
        </div>

        <div className="space-y-3 max-h-[400px] overflow-y-auto no-scrollbar">
          {tasks.length === 0 ? (
            <div className="text-center py-8 text-white/60">
              <p>No tasks for today</p>
              <p className="text-sm mt-1">Add your first task above!</p>
            </div>
          ) : (
            tasks.map((task) => (
              <div
                key={task.id}
                className={`card-glass p-4 animate-slide-up ${
                  task.completed ? "opacity-70" : ""
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center flex-1">
                    <button
                      onClick={() => toggleComplete(task.id)}
                      className={`w-5 h-5 rounded-full mr-3 border-2 flex items-center justify-center transition-all duration-300 ${
                        task.completed
                          ? "bg-neon-blue border-neon-blue"
                          : "border-white/30 hover:border-neon-blue/60"
                      }`}
                    >
                      {task.completed && <Check className="h-3 w-3 text-white" />}
                    </button>

                    {editingId === task.id ? (
                      <div className="flex items-center flex-1 gap-2">
                        <Input
                          value={editingText}
                          onChange={(e) => setEditingText(e.target.value)}
                          className="bg-glass/50 backdrop-blur-sm border border-neon-blue/20 text-white text-sm"
                          onKeyDown={(e) => {
                            if (e.key === "Enter") saveEdit();
                            if (e.key === "Escape") cancelEdit();
                          }}
                          autoFocus
                        />
                        <button
                          onClick={saveEdit}
                          className="px-2 py-1 rounded bg-neon-blue/20 border border-neon-blue/40 text-white hover:bg-neon-blue/30 transition-all duration-300"
                          title="Save"
                        >
                          <Save className="h-3 w-3" />
                        </button>
                        <button
                          onClick={cancelEdit}
                          className="px-2 py-1 rounded bg-glass/50 border border-white/20 text-white hover:border-white/40 transition-all duration-300"
                          title="Cancel"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </div>
                    ) : (
                      <span
                        className={`text-sm flex-1 ${
                          task.completed ? "line-through text-white/50" : "text-white"
                        }`}
                      >
                        {task.title}
                      </span>
                    )}
                  </div>

                  {editingId !== task.id && (
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => startEdit(task.id, task.title)}
                        className="px-2 py-1 rounded bg-glass/50 border border-neon-purple/20 text-white hover:border-neon-purple/40 transition-all duration-300"
                        title="Edit task"
                      >
                        <Edit3 className="h-3 w-3" />
                      </button>
                      <button
                        onClick={() => deleteTask(task.id)}
                        className="px-2 py-1 rounded bg-glass/50 border border-red-500/20 text-white hover:border-red-500/40 transition-all duration-300"
                        title="Delete task"
                      >
                        <Trash2 className="h-3 w-3" />
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default TaskComponent;
