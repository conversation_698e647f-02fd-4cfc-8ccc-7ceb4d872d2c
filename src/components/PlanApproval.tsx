
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ArrowUp } from "lucide-react";
import { useState, useEffect } from "react";

interface PlanApprovalProps {
  onNavigate: (page: string) => void;
}

// Define proper types for our meal plan data
interface MealItem {
  meal: string;
  calories: number;
  protein: string;
  carbs: string;
  fat: string;
}

interface MealPlan {
  breakfast: MealItem;
  lunch: MealItem;
  dinner: MealItem;
  snacks: MealItem[];
}

interface WorkoutDay {
  day: string;
  workout: string;
  duration: string;
  exercises: string[];
}

interface HydrationPlan {
  goal: string;
  schedule: string[];
}

interface SleepPlan {
  target: string;
  bedtime: string;
  wakeup: string;
  tips: string[];
}

interface FitnessPlan {
  workoutPlan: WorkoutDay[];
  mealPlan: MealPlan;
  hydration: HydrationPlan;
  sleep: SleepPlan;
}

const PlanApproval = ({ onNavigate }: PlanApprovalProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const [generatedPlan, setGeneratedPlan] = useState<FitnessPlan | null>(null);

  useEffect(() => {
    // Load the generated plan from localStorage
    const savedPlan = localStorage.getItem('vitalEdgeGeneratedPlan');
    const formData = localStorage.getItem('vitalEdgeFormData');

    console.log('📋 Loading plan approval data...');
    console.log('- Saved plan:', savedPlan ? 'Found' : 'Not found');
    console.log('- Form data:', formData ? 'Found' : 'Not found');

    if (savedPlan) {
      try {
        const plan = JSON.parse(savedPlan);
        console.log('✅ Plan loaded successfully:', plan);
        setGeneratedPlan(plan);
      } catch (error) {
        console.error('❌ Error parsing saved plan:', error);
      }
    }

    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1500);
    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center">
        <div className="text-center animate-pulse">
          <div className="w-20 h-20 border-4 border-vital-lime border-t-transparent rounded-full animate-spin mx-auto mb-8"></div>
          <h2 className="text-2xl font-bold text-vital-lime mb-4">Generating Your Perfect Plan</h2>
          <p className="text-gray-400">Our AI is crafting a personalized experience just for you...</p>
        </div>
      </div>
    );
  }

  // Use generated plan or fallback to sample plan
  const displayPlan: FitnessPlan = generatedPlan || {
    workoutPlan: [
      { day: "Monday", workout: "Upper Body Strength", duration: "60 min", exercises: ["Push-ups", "Pull-ups", "Dumbbell rows", "Shoulder press"] },
      { day: "Tuesday", workout: "Cardio & Core", duration: "45 min", exercises: ["Running", "Planks", "Russian twists", "Bicycle crunches"] },
      { day: "Wednesday", workout: "Lower Body Power", duration: "60 min", exercises: ["Squats", "Deadlifts", "Lunges", "Calf raises"] },
      { day: "Thursday", workout: "Active Recovery", duration: "30 min", exercises: ["Yoga", "Stretching", "Light walking"] },
      { day: "Friday", workout: "Full Body Circuit", duration: "50 min", exercises: ["Burpees", "Mountain climbers", "Kettlebell swings"] }
    ],
    mealPlan: {
      breakfast: { meal: "Protein Oatmeal Bowl", calories: 350, protein: "25g", carbs: "45g", fat: "8g" },
      lunch: { meal: "Grilled Chicken Salad", calories: 420, protein: "35g", carbs: "20g", fat: "18g" },
      dinner: { meal: "Salmon with Sweet Potato", calories: 480, protein: "40g", carbs: "35g", fat: "22g" },
      snacks: [
        { meal: "Greek Yogurt with Berries", calories: 150, protein: "15g", carbs: "18g", fat: "3g" },
        { meal: "Almonds (small handful)", calories: 160, protein: "6g", carbs: "6g", fat: "14g" }
      ]
    },
    hydration: {
      goal: "3 Liters",
      schedule: ["Wake up: 500ml", "Pre-workout: 300ml", "During workout: 400ml", "Post-workout: 500ml", "Evening: 300ml"]
    },
    sleep: {
      target: "8 hours",
      bedtime: "10:30 PM",
      wakeup: "6:30 AM",
      tips: ["No screens 1 hour before bed", "Keep room cool", "Consistent schedule"]
    }
  };

  return (
    <div className="min-h-screen bg-black text-white p-6 app-scrollable">
      {/* Header */}
      <div className="container mx-auto max-w-4xl">
        <div className="flex items-center justify-between mb-8">
          <Button
            variant="ghost"
            onClick={() => onNavigate('generate')}
            className="text-vital-lime hover:bg-vital-lime/10"
          >
            <ArrowUp className="h-4 w-4 mr-2 rotate-[-90deg]" />
            Back
          </Button>
          <div className="text-2xl font-bold text-vital-lime">VitalEdge</div>
          <div className="text-sm text-gray-400">Your Plan</div>
        </div>

        {/* Success Message */}
        <div className="text-center mb-8 animate-fade-in">
          <div className="text-6xl mb-4">🎉</div>
          <h1 className="text-4xl font-bold text-vital-lime mb-4">Your Plan is Ready!</h1>
          <p className="text-gray-400 text-lg">Personalized for your goals and lifestyle</p>
        </div>

        {/* Plan Tabs */}
        <Tabs defaultValue="workout" className="w-full animate-scale-in">
          <TabsList className="grid w-full grid-cols-4 bg-vital-gray-dark border border-vital-lime/20">
            <TabsTrigger value="workout" className="data-[state=active]:bg-vital-lime data-[state=active]:text-black">Workout</TabsTrigger>
            <TabsTrigger value="nutrition" className="data-[state=active]:bg-vital-lime data-[state=active]:text-black">Nutrition</TabsTrigger>
            <TabsTrigger value="hydration" className="data-[state=active]:bg-vital-lime data-[state=active]:text-black">Hydration</TabsTrigger>
            <TabsTrigger value="sleep" className="data-[state=active]:bg-vital-lime data-[state=active]:text-black">Sleep</TabsTrigger>
          </TabsList>

          <TabsContent value="workout" className="mt-6">
            <div className="grid gap-4">
              {displayPlan.workoutPlan.map((day, index) => (
                <Card key={index} className="bg-vital-gray-dark border-vital-lime/20 hover:border-vital-lime/50 transition-all duration-300">
                  <CardHeader>
                    <CardTitle className="flex justify-between items-center">
                      <span className="text-vital-lime">{day.day}</span>
                      <span className="text-sm text-gray-400">{day.duration}</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <h4 className="font-semibold text-white mb-3">{day.workout}</h4>
                    <div className="flex flex-wrap gap-2">
                      {day.exercises.map((exercise, i) => (
                        <span key={i} className="bg-vital-lime/20 text-vital-lime px-3 py-1 rounded-full text-sm">
                          {exercise}
                        </span>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="nutrition" className="mt-6">
            <div className="space-y-6">
              {/* Main Meals */}
              <div className="grid md:grid-cols-3 gap-4">
                {/* Render breakfast, lunch, and dinner separately since they're individual objects, not an array */}
                <Card className="bg-vital-gray-dark border-vital-lime/20">
                  <CardHeader>
                    <CardTitle className="text-vital-lime">Breakfast</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <h4 className="font-semibold text-white mb-3">{displayPlan.mealPlan.breakfast.meal}</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="text-gray-400">Calories: <span className="text-white">{displayPlan.mealPlan.breakfast.calories}</span></div>
                      <div className="text-gray-400">Protein: <span className="text-white">{displayPlan.mealPlan.breakfast.protein}</span></div>
                      <div className="text-gray-400">Carbs: <span className="text-white">{displayPlan.mealPlan.breakfast.carbs}</span></div>
                      <div className="text-gray-400">Fat: <span className="text-white">{displayPlan.mealPlan.breakfast.fat}</span></div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-vital-gray-dark border-vital-lime/20">
                  <CardHeader>
                    <CardTitle className="text-vital-lime">Lunch</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <h4 className="font-semibold text-white mb-3">{displayPlan.mealPlan.lunch.meal}</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="text-gray-400">Calories: <span className="text-white">{displayPlan.mealPlan.lunch.calories}</span></div>
                      <div className="text-gray-400">Protein: <span className="text-white">{displayPlan.mealPlan.lunch.protein}</span></div>
                      <div className="text-gray-400">Carbs: <span className="text-white">{displayPlan.mealPlan.lunch.carbs}</span></div>
                      <div className="text-gray-400">Fat: <span className="text-white">{displayPlan.mealPlan.lunch.fat}</span></div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-vital-gray-dark border-vital-lime/20">
                  <CardHeader>
                    <CardTitle className="text-vital-lime">Dinner</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <h4 className="font-semibold text-white mb-3">{displayPlan.mealPlan.dinner.meal}</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="text-gray-400">Calories: <span className="text-white">{displayPlan.mealPlan.dinner.calories}</span></div>
                      <div className="text-gray-400">Protein: <span className="text-white">{displayPlan.mealPlan.dinner.protein}</span></div>
                      <div className="text-gray-400">Carbs: <span className="text-white">{displayPlan.mealPlan.dinner.carbs}</span></div>
                      <div className="text-gray-400">Fat: <span className="text-white">{displayPlan.mealPlan.dinner.fat}</span></div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Snacks */}
              <Card className="bg-vital-gray-dark border-vital-lime/20">
                <CardHeader>
                  <CardTitle className="text-vital-lime">Snacks</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid md:grid-cols-2 gap-4">
                    {displayPlan.mealPlan.snacks.map((snack, index) => (
                      <div key={index} className="border border-vital-lime/20 p-4 rounded-lg">
                        <h4 className="font-semibold text-white mb-2">{snack.meal}</h4>
                        <div className="text-sm text-gray-400">
                          {snack.calories} cal | {snack.protein} protein | {snack.carbs} carbs | {snack.fat} fat
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="hydration" className="mt-6">
            <Card className="bg-vital-gray-dark border-vital-lime/20">
              <CardHeader>
                <CardTitle className="text-vital-lime">Daily Hydration Plan</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="mb-6">
                  <h3 className="text-2xl font-bold text-white mb-2">Goal: {displayPlan.hydration.goal}</h3>
                  <p className="text-gray-400">Stay hydrated throughout your day with this optimized schedule</p>
                </div>
                <div className="space-y-3">
                  {displayPlan.hydration.schedule.map((item, index) => (
                    <div key={index} className="flex items-center space-x-3 p-3 bg-vital-gray-medium/50 rounded-lg">
                      <div className="w-3 h-3 bg-vital-lime rounded-full"></div>
                      <span className="text-white">{item}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="sleep" className="mt-6">
            <Card className="bg-vital-gray-dark border-vital-lime/20">
              <CardHeader>
                <CardTitle className="text-vital-lime">Sleep Optimization</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-xl font-bold text-white mb-4">Sleep Schedule</h3>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Target Duration:</span>
                        <span className="text-vital-lime font-semibold">{displayPlan.sleep.target}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Bedtime:</span>
                        <span className="text-white">{displayPlan.sleep.bedtime}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Wake up:</span>
                        <span className="text-white">{displayPlan.sleep.wakeup}</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-white mb-4">Sleep Tips</h3>
                    <ul className="space-y-2">
                      {displayPlan.sleep.tips.map((tip, index) => (
                        <li key={index} className="flex items-center space-x-2">
                          <div className="w-2 h-2 bg-vital-lime rounded-full"></div>
                          <span className="text-gray-300">{tip}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Action Buttons */}
        <div className="flex gap-4 mt-8 animate-slide-up">
          <Button
            variant="outline"
            className="flex-1 border-vital-lime text-vital-lime hover:bg-vital-lime hover:text-black transition-all duration-300"
            onClick={() => onNavigate('generate')}
          >
            Regenerate Plan
          </Button>
          <Button
            onClick={() => onNavigate('dashboard')}
            className="flex-1 bg-vital-lime text-black hover:bg-vital-lime-bright font-bold glow-button transition-all duration-300 hover:scale-105"
          >
            Looks Great! Let's Start
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PlanApproval;
