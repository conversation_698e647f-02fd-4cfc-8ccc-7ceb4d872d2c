
import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>Up, Loader2, Maximize2, <PERSON>mize2, Trash2, <PERSON>ota<PERSON><PERSON>cw, ArrowDown } from "lucide-react";
import { aiService, type UserData } from "@/services/aiService";
import MarkdownMessage from "./MarkdownMessage";

interface Message {
  id: string;
  content: string;
  sender: "user" | "bot";
  timestamp: string;
  isLoading?: boolean;
}

interface ChatBotProps {
  userData?: UserData;
  isFullscreen?: boolean;
  onToggleFullscreen?: () => void;
}

const initMessages: Message[] = [
  {
    id: "1",
    content: "Hi there! I'm your VitalEdge assistant. How can I help with your health and fitness goals today?",
    sender: "bot",
    timestamp: new Date().toISOString(),
  },
];

const ChatBot = ({ userData, isFullscreen = false, onToggleFullscreen }: ChatBotProps) => {
  console.log('🎯 ChatBot component initialized');
  console.log('- User data received:', userData);
  console.log('- Fullscreen mode:', isFullscreen);

  const [messages, setMessages] = useState<Message[]>(() => {
    const savedMessages = localStorage.getItem("vitalEdgeChatMessages");
    const initialMessages = savedMessages ? JSON.parse(savedMessages) : initMessages;
    console.log('- Initial messages loaded:', initialMessages.length);
    return initialMessages;
  });
  const [inputValue, setInputValue] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [streamingMessageId, setStreamingMessageId] = useState<string | null>(null);
  const [showClearConfirm, setShowClearConfirm] = useState(false);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    localStorage.setItem("vitalEdgeChatMessages", JSON.stringify(messages));
  }, [messages]);

  // Auto-scroll effect - separate from messages to avoid conflicts
  useEffect(() => {
    const scrollToBottom = () => {
      if (messagesContainerRef.current) {
        messagesContainerRef.current.scrollTo({
          top: messagesContainerRef.current.scrollHeight,
          behavior: 'smooth'
        });
        setShowScrollToBottom(false);
      }
    };

    // Scroll immediately
    scrollToBottom();

    // Also scroll after a short delay to ensure content is rendered
    const timeoutId = setTimeout(scrollToBottom, 100);

    return () => clearTimeout(timeoutId);
  }, [messages.length, streamingMessageId]);

  // Scroll detection to show/hide scroll-to-bottom button
  useEffect(() => {
    const scrollContainer = messagesContainerRef.current;
    if (!scrollContainer) return;

    const handleScroll = () => {
      const isNearBottom = scrollContainer.scrollTop + scrollContainer.clientHeight >= scrollContainer.scrollHeight - 100;
      setShowScrollToBottom(!isNearBottom && messages.length > 2);
    };

    scrollContainer.addEventListener('scroll', handleScroll);
    return () => scrollContainer.removeEventListener('scroll', handleScroll);
  }, [messages.length]);

  const scrollToBottom = () => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTo({
        top: messagesContainerRef.current.scrollHeight,
        behavior: 'smooth'
      });
      setShowScrollToBottom(false);
    }
  };

  const handleClearChat = () => {
    setMessages(initMessages);
    localStorage.removeItem("vitalEdgeChatMessages");
    setShowClearConfirm(false);
    console.log('🗑️ Chat history cleared');
  };

  const handleSendMessage = async () => {
    console.log('💬 ChatBot - handleSendMessage called');
    console.log('- Input value:', inputValue);
    console.log('- Is loading:', isLoading);

    if (!inputValue.trim() || isLoading) {
      console.log('❌ Message send blocked - empty input or already loading');
      return;
    }

    const userInput = inputValue.trim();
    console.log('📤 Sending message:', userInput);
    console.log('👤 User data being passed:', userData);

    setInputValue("");
    setIsLoading(true);

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      content: userInput,
      sender: "user",
      timestamp: new Date().toISOString(),
    };

    console.log('➕ Adding user message to chat');
    setMessages((prev) => {
      const newMessages = [...prev, userMessage];
      console.log('- Total messages after user message:', newMessages.length);
      return newMessages;
    });

    // Add loading message
    const loadingMessage: Message = {
      id: (Date.now() + 1).toString(),
      content: "Thinking...",
      sender: "bot",
      timestamp: new Date().toISOString(),
      isLoading: true,
    };

    console.log('⏳ Adding loading message');
    setMessages((prev) => {
      const newMessages = [...prev, loadingMessage];
      console.log('- Total messages after loading message:', newMessages.length);
      return newMessages;
    });

    try {
      console.log('🔄 Calling AI service...');
      const startTime = Date.now();

      // Set up streaming callback
      const streamingId = loadingMessage.id;
      setStreamingMessageId(streamingId);

      const handleStream = (streamedContent: string) => {
        console.log('🌊 Streaming update received, length:', streamedContent.length);
        setMessages((prev) =>
          prev.map((msg) =>
            msg.id === streamingId
              ? { ...msg, content: streamedContent, isLoading: false }
              : msg
          )
        );

        // Immediate scroll during streaming for responsiveness
        requestAnimationFrame(() => {
          if (messagesContainerRef.current) {
            // Check if user is near bottom before auto-scrolling
            const isNearBottom = messagesContainerRef.current.scrollTop + messagesContainerRef.current.clientHeight >= messagesContainerRef.current.scrollHeight - 100;
            if (isNearBottom) {
              messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
            }
          }
        });
      };

      // Generate AI response with streaming
      const responseContent = await aiService.generateResponse(userInput, userData, handleStream);

      const endTime = Date.now();
      console.log('✅ AI response received in', endTime - startTime, 'ms');
      console.log('- Final response content length:', responseContent.length);

      // Ensure final content is set (in case streaming didn't work)
      console.log('🔄 Setting final AI response');
      setMessages((prev) => {
        const updatedMessages = prev.map((msg) =>
          msg.id === streamingId
            ? { ...msg, content: responseContent, isLoading: false }
            : msg
        );
        console.log('- Messages updated, total count:', updatedMessages.length);
        return updatedMessages;
      });
    } catch (error) {
      console.error('💥 Error in ChatBot handleSendMessage:', error);

      // Replace loading message with error message
      console.log('🔄 Replacing loading message with error message');
      setMessages((prev) =>
        prev.map((msg) =>
          msg.isLoading
            ? {
                ...msg,
                content: "I'm having trouble connecting right now. Please try again in a moment.",
                isLoading: false
              }
            : msg
        )
      );
    } finally {
      console.log('🏁 Setting loading to false and clearing streaming ID');
      setIsLoading(false);
      setStreamingMessageId(null);
    }
  };

  const containerClass = isFullscreen
    ? "fixed inset-0 z-50 bg-gradient-background"
    : "h-full flex flex-col";

  const chatClass = isFullscreen
    ? "h-screen flex flex-col"
    : "h-full flex flex-col relative overflow-hidden";

  return (
    <div className={containerClass}>
      <div className={chatClass}>
        {/* Background Effects - only for fullscreen */}
        {isFullscreen && (
          <>
            <div className="absolute top-0 right-0 w-40 h-40 bg-neon-purple/10 rounded-full blur-3xl -mr-10 -mt-10 pointer-events-none"></div>
            <div className="absolute bottom-0 left-0 w-32 h-32 bg-neon-blue/10 rounded-full blur-3xl -ml-10 -mb-10 pointer-events-none"></div>
          </>
        )}

        {/* Header */}
        <div className={`px-6 py-4 border-b border-white/5 relative flex-shrink-0 ${isFullscreen ? 'card-glass' : 'bg-gradient-to-r from-neumorphic-dark/80 to-neumorphic-dark/60 backdrop-blur-sm'}`}>
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-purple">VitalEdge Assistant</h2>
              <p className="text-white/70 text-sm">Ask me anything about fitness and health</p>
            </div>
            <div className="flex items-center gap-2">
              {/* Clear Chat Button */}
              {!showClearConfirm ? (
                <button
                  onClick={() => setShowClearConfirm(true)}
                  className="flex items-center gap-1 text-xs bg-glass/50 border border-red-500/20 text-white px-2 py-1 rounded hover:border-red-500/40 transition-all duration-300"
                  title="Clear chat history"
                >
                  <Trash2 className="h-3 w-3" />
                  Clear
                </button>
              ) : (
                <div className="flex items-center gap-1">
                  <button
                    onClick={handleClearChat}
                    className="text-xs bg-red-500/20 border border-red-500/40 text-white px-2 py-1 rounded hover:bg-red-500/30 transition-all duration-300"
                  >
                    Confirm
                  </button>
                  <button
                    onClick={() => setShowClearConfirm(false)}
                    className="text-xs bg-glass/50 border border-white/20 text-white px-2 py-1 rounded hover:border-white/40 transition-all duration-300"
                  >
                    Cancel
                  </button>
                </div>
              )}

              {/* Fullscreen Toggle */}
              {onToggleFullscreen && (
                <button
                  onClick={onToggleFullscreen}
                  className="flex items-center gap-1 text-xs bg-glass/50 border border-neon-purple/20 text-white px-2 py-1 rounded hover:border-neon-purple/40 transition-all duration-300"
                  title={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
                >
                  {isFullscreen ? <Minimize2 className="h-3 w-3" /> : <Maximize2 className="h-3 w-3" />}
                  {isFullscreen ? "Exit" : "Full"}
                </button>
              )}

              {/* Debug Button */}
              <button
                onClick={() => {
                  console.log('🔍 Debug Info:');
                  console.log('- Messages count:', messages.length);
                  console.log('- Is loading:', isLoading);
                  console.log('- Input value:', inputValue);
                  console.log('- User data:', userData);
                  console.log('- Environment variables:', import.meta.env);
                  console.log('- AI Service test...');
                  aiService.generateResponse('test message', userData).then(response => {
                    console.log('- AI Service test response:', response);
                  });
                }}
                className="text-xs bg-glass/50 border border-neon-blue/20 text-white px-2 py-1 rounded hover:border-neon-blue/40 transition-all duration-300"
                title="Debug information"
              >
                Debug
              </button>


            </div>
          </div>
        </div>

        {/* Messages Area */}
        <div className="flex-1 relative overflow-hidden flex flex-col">
          {/* Scroll to Bottom Button */}
          {showScrollToBottom && (
            <button
              onClick={scrollToBottom}
              className="absolute bottom-4 right-4 z-10 bg-neon-blue/80 hover:bg-neon-blue text-white p-2 rounded-full shadow-lg backdrop-blur-sm border border-neon-blue/20 transition-all duration-300 hover:scale-110"
              title="Scroll to bottom"
            >
              <ArrowDown className="h-4 w-4" />
            </button>
          )}

          {/* Custom Scrollable Container */}
          <div
            ref={messagesContainerRef}
            className="flex-1 overflow-y-auto p-4 custom-scrollbar"
            style={{
              scrollbarWidth: 'thin',
              scrollbarColor: '#3b82f6 transparent'
            }}
          >
            <div className="space-y-4 pb-4">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${
                    message.sender === "user" ? "justify-end" : "justify-start"
                  }`}
                >
                  <div
                    className={`max-w-[80%] rounded-xl px-4 py-3 backdrop-blur-sm transition-all duration-300 ${
                      message.sender === "user"
                        ? "bg-gradient-blue text-white shadow-glow-blue"
                        : "card-glass border border-white/10"
                    }`}
                  >
                    <div className="flex items-start gap-2">
                      {message.isLoading && (
                        <div className="flex items-center gap-1 mt-1">
                          <div className="flex space-x-1">
                            <div className="w-2 h-2 bg-neon-blue rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                            <div className="w-2 h-2 bg-neon-blue rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                            <div className="w-2 h-2 bg-neon-blue rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                          </div>
                        </div>
                      )}
                      <div className="flex-1">
                        {message.sender === "user" ? (
                          <p className="text-white">{message.content}</p>
                        ) : (
                          <MarkdownMessage
                            content={message.content}
                            className="text-white"
                          />
                        )}
                      </div>
                    </div>
                    <p className={`text-xs mt-2 ${
                      message.sender === "user" ? "text-white/80" : "text-white/60"
                    }`}>
                      {new Date(message.timestamp).toLocaleTimeString([], {
                        hour: "2-digit",
                        minute: "2-digit",
                      })}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Input Area */}
        <div className={`p-4 pb-6 border-t border-white/5 relative flex-shrink-0 ${isFullscreen ? 'card-glass' : 'bg-gradient-to-r from-neumorphic-dark/80 to-neumorphic-dark/60 backdrop-blur-sm'}`}>
          <form
            onSubmit={(e) => {
              e.preventDefault();
              handleSendMessage();
            }}
            className="flex items-center gap-3 mb-4"
          >
            <Input
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder="Ask something..."
              disabled={isLoading}
              className="bg-glass/50 backdrop-blur-sm border border-neon-purple/20 text-white placeholder:text-white/60 focus:border-neon-purple/40 focus:shadow-glow-purple outline-none transition-all duration-300 flex-1"
              autoFocus={!isLoading}
            />
            <Button
              type="submit"
              size="icon"
              disabled={!inputValue.trim() || isLoading}
              className="btn-glow-purple text-white h-10 w-10 flex-shrink-0"
            >
              {isLoading ? (
                <Loader2 className="h-5 w-5 animate-spin" />
              ) : (
                <ArrowUp className="h-5 w-5" />
              )}
            </Button>
          </form>

          {/* Disclaimer */}
          <div className="text-center text-xs text-white/40 leading-relaxed">
            <p className="mb-1">
              🤖 <strong>VitalEdge AI Assistant</strong> - Your personal fitness & health companion
            </p>
            <p>
              AI responses are for informational purposes only. Always consult healthcare professionals for medical advice.
              <br className="hidden sm:block" />
              This AI may occasionally generate incorrect information.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatBot;
