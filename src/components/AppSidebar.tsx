
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Sidebar,
  <PERSON>bar<PERSON>ontent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarTrigger
} from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import { ArrowUp, Calendar, Check, User, Settings, Layout, List, Bell, Cat } from "lucide-react";

interface AppSidebarProps {
  onNavigate: (page: string) => void;
  activeTab: string;
  setActiveTab: (tab: string) => void;
  streak: number;
  xp: number;
}

const AppSidebar = ({ onNavigate, activeTab, setActiveTab, streak, xp }: AppSidebarProps) => {
  const navigate = useNavigate();

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    navigate(`/dashboard/${tab}`);
  };

  return (
    <Sidebar side="left" variant="sidebar" className="bg-gradient-background">
      <SidebarHeader className="p-5">
        <div className="flex items-center justify-between">
          <div className="text-xl font-bold bg-clip-text text-transparent bg-gradient-blue">VitalEdge</div>
          <SidebarTrigger className="text-white hover:text-neon-blue transition-colors" />
        </div>

        <div className="card-glass mt-6 p-4 rounded-xl">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-12 h-12 rounded-full bg-gradient-blue shadow-glow-blue flex items-center justify-center text-white font-bold">
              {streak}
            </div>
            <div>
              <p className="text-xs text-white/60">Day Streak</p>
              <p className="text-sm font-medium text-white">{streak} days</p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 rounded-full bg-gradient-purple shadow-glow-purple flex items-center justify-center text-white font-bold">
              XP
            </div>
            <div>
              <p className="text-xs text-white/60">Experience</p>
              <p className="text-sm font-medium text-white">{xp} points</p>
            </div>
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent className="p-4">
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              isActive={activeTab === 'overview'}
              onClick={() => handleTabChange('overview')}
              tooltip="Overview"
              className={`rounded-lg transition-all duration-300 ${activeTab === 'overview' ? 'bg-glass' : 'hover:bg-glass/50'}`}
            >
              <Layout size={20} className={activeTab === 'overview' ? 'text-neon-blue' : 'text-white'} />
              <span className={activeTab === 'overview' ? 'text-neon-blue' : 'text-white'}>Overview</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton
              isActive={activeTab === 'tasks'}
              onClick={() => handleTabChange('tasks')}
              tooltip="Tasks"
              className={`rounded-lg transition-all duration-300 ${activeTab === 'tasks' ? 'bg-glass' : 'hover:bg-glass/50'}`}
            >
              <Check size={20} className={activeTab === 'tasks' ? 'text-neon-blue' : 'text-white'} />
              <span className={activeTab === 'tasks' ? 'text-neon-blue' : 'text-white'}>Tasks</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton
              isActive={activeTab === 'calendar'}
              onClick={() => handleTabChange('calendar')}
              tooltip="Calendar"
              className={`rounded-lg transition-all duration-300 ${activeTab === 'calendar' ? 'bg-glass' : 'hover:bg-glass/50'}`}
            >
              <Calendar size={20} className={activeTab === 'calendar' ? 'text-neon-purple' : 'text-white'} />
              <span className={activeTab === 'calendar' ? 'text-neon-purple' : 'text-white'}>Calendar</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton
              isActive={activeTab === 'stats'}
              onClick={() => handleTabChange('stats')}
              tooltip="Statistics"
              className={`rounded-lg transition-all duration-300 ${activeTab === 'stats' ? 'bg-glass' : 'hover:bg-glass/50'}`}
            >
              <ArrowUp size={20} className={activeTab === 'stats' ? 'text-neon-blue' : 'text-white'} />
              <span className={activeTab === 'stats' ? 'text-neon-blue' : 'text-white'}>Statistics</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton
              isActive={activeTab === 'reminders'}
              onClick={() => handleTabChange('reminders')}
              tooltip="Reminders"
              className={`rounded-lg transition-all duration-300 ${activeTab === 'reminders' ? 'bg-glass' : 'hover:bg-glass/50'}`}
            >
              <Bell size={20} className={activeTab === 'reminders' ? 'text-neon-purple' : 'text-white'} />
              <span className={activeTab === 'reminders' ? 'text-neon-purple' : 'text-white'}>Reminders</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton
              isActive={activeTab === 'chat'}
              onClick={() => handleTabChange('chat')}
              tooltip="Chat Assistant"
              className={`rounded-lg transition-all duration-300 ${activeTab === 'chat' ? 'bg-glass' : 'hover:bg-glass/50'}`}
            >
              <Cat size={20} className={activeTab === 'chat' ? 'text-neon-blue' : 'text-white'} />
              <span className={activeTab === 'chat' ? 'text-neon-blue' : 'text-white'}>Assistant</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarContent>

      <SidebarFooter className="p-4 mt-auto">
        <div className="card-glass p-3 mb-4 rounded-xl">
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton
                tooltip="Profile"
                className="hover:bg-glass/50 rounded-lg transition-all duration-300"
              >
                <User size={20} className="text-white" />
                <span className="text-white">Profile</span>
              </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem>
              <SidebarMenuButton
                tooltip="Settings"
                className="hover:bg-glass/50 rounded-lg transition-all duration-300"
              >
                <Settings size={20} className="text-white" />
                <span className="text-white">Settings</span>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </div>
        <button
          onClick={() => onNavigate('generate')}
          className="w-full py-3 rounded-lg btn-glow-purple text-white font-medium"
        >
          Generate New Plan
        </button>
      </SidebarFooter>
    </Sidebar>
  );
};

export default AppSidebar;
