import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import { ArrowUp, ArrowRight, Dumbbell, Clock, Salad, Zap } from "lucide-react";
import { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import { aiService } from "@/services/aiService";

interface GeneratePlanProps {
  onNavigate: (page: string) => void;
}

// Array of motivational quotes
const motivationalQuotes = [
  "The only bad workout is the one that didn't happen.",
  "Your body can stand almost anything. It's your mind you have to convince.",
  "The difference between try and triumph is just a little umph.",
  "Strive for progress, not perfection.",
  "The hardest lift of all is lifting your butt off the couch.",
  "Your health is an investment, not an expense.",
  "Don't wish for it, work for it.",
  "Fitness is not about being better than someone else. It's about being better than you used to be."
];

const GeneratePlan = ({ onNavigate }: GeneratePlanProps) => {
  const [currentStep, setCurrentStep] = useState(1);
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    goal: '',
    workoutDays: '',
    workoutHours: '',
    dietaryRestrictions: '',
    waterIntake: '',
    sleepHours: '',
    gender: '',
    age: '',
    height: '',
    weight: ''
  });
  const [quote, setQuote] = useState("");
  const [isHovering, setIsHovering] = useState(false);

  const totalSteps = 3;
  const progress = (currentStep / totalSteps) * 100;

  useEffect(() => {
    // Display a random motivational quote on component mount
    const randomQuote = motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];
    setQuote(randomQuote);

    // Save form data to local storage when it changes
    const savedData = localStorage.getItem('vitalEdgeFormData');
    if (savedData) {
      setFormData(JSON.parse(savedData));
    }
  }, []);

  useEffect(() => {
    // Save form data to local storage when it changes
    localStorage.setItem('vitalEdgeFormData', JSON.stringify(formData));
  }, [formData]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const showTooltip = (message: string) => {
    toast({
      title: "Tip",
      description: message,
    });
  };

  const handleNext = async () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    } else {
      // Validate form data
      const requiredFields = ['goal', 'workoutDays', 'workoutHours', 'gender', 'age', 'height', 'weight'];
      const missingFields = requiredFields.filter(field => !formData[field]);

      if (missingFields.length > 0) {
        toast({
          title: "Missing Information",
          description: `Please fill in: ${missingFields.join(', ')}`,
          variant: "destructive"
        });
        return;
      }

      // Show loading toast
      toast({
        title: "Generating your personalized plan",
        description: "Our AI is analyzing your data and creating the perfect fitness solution...",
      });

      try {
        // Generate plan using AI service
        console.log('🚀 Generating plan with form data:', formData);
        const generatedPlan = await aiService.generatePlan(formData);

        // Save both form data and generated plan
        localStorage.setItem('vitalEdgeFormData', JSON.stringify(formData));
        localStorage.setItem('vitalEdgeGeneratedPlan', JSON.stringify(generatedPlan));

        toast({
          title: "Plan Generated Successfully! 🎉",
          description: "Your personalized fitness plan is ready for review.",
        });

        // Navigate to approval page
        setTimeout(() => {
          onNavigate('approval');
        }, 1000);

      } catch (error) {
        console.error('❌ Error generating plan:', error);
        toast({
          title: "Generation Failed",
          description: "There was an error creating your plan. Please try again.",
          variant: "destructive"
        });
      }
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      onNavigate('landing');
    }
  };

  const getStepName = () => {
    switch (currentStep) {
      case 1: return "Goals";
      case 2: return "Lifestyle";
      case 3: return "Metrics";
      default: return "";
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6 animate-fade-in">
            <div>
              <Label
                className="text-vital-lime text-lg font-semibold flex items-center gap-2"
                onMouseEnter={() => showTooltip("Selecting the right goal helps us tailor your workouts.")}
              >
                <Dumbbell size={18} />
                What's your primary fitness goal?
              </Label>
              <Select onValueChange={(value) => handleInputChange('goal', value)}>
                <SelectTrigger className="bg-vital-gray-medium border-vital-lime/30 text-white mt-2 focus:ring-vital-lime focus:border-vital-lime focus:ring-opacity-50 transition-all">
                  <SelectValue placeholder="e.g., Gain Muscle" />
                </SelectTrigger>
                <SelectContent className="bg-vital-gray-dark border-vital-lime/30">
                  <SelectItem value="lose-fat">Lose Fat</SelectItem>
                  <SelectItem value="gain-muscle">Gain Muscle</SelectItem>
                  <SelectItem value="maintain">Maintain Weight</SelectItem>
                  <SelectItem value="general-fitness">General Fitness</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label
                  className="text-vital-lime text-lg font-semibold flex items-center gap-2"
                  onMouseEnter={() => showTooltip("Consistency is key! Choose a realistic schedule.")}
                >
                  <Clock size={18} />
                  Workout Days/Week
                </Label>
                <Select onValueChange={(value) => handleInputChange('workoutDays', value)}>
                  <SelectTrigger className="bg-vital-gray-medium border-vital-lime/30 text-white mt-2 focus:ring-vital-lime focus:border-vital-lime focus:ring-opacity-50 transition-all">
                    <SelectValue placeholder="e.g., 5 Days" />
                  </SelectTrigger>
                  <SelectContent className="bg-vital-gray-dark border-vital-lime/30">
                    <SelectItem value="3">3 days</SelectItem>
                    <SelectItem value="4">4 days</SelectItem>
                    <SelectItem value="5">5 days</SelectItem>
                    <SelectItem value="6">6 days</SelectItem>
                    <SelectItem value="7">7 days</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label
                  className="text-vital-lime text-lg font-semibold flex items-center gap-2"
                  onMouseEnter={() => showTooltip("Quality over quantity! Focus on effective workouts.")}
                >
                  <Clock size={18} />
                  Hours per Session
                </Label>
                <Select onValueChange={(value) => handleInputChange('workoutHours', value)}>
                  <SelectTrigger className="bg-vital-gray-medium border-vital-lime/30 text-white mt-2 focus:ring-vital-lime focus:border-vital-lime focus:ring-opacity-50 transition-all">
                    <SelectValue placeholder="e.g., 1.5 Hours" />
                  </SelectTrigger>
                  <SelectContent className="bg-vital-gray-dark border-vital-lime/30">
                    <SelectItem value="0.5">30 minutes</SelectItem>
                    <SelectItem value="1">1 hour</SelectItem>
                    <SelectItem value="1.5">1.5 hours</SelectItem>
                    <SelectItem value="2">2 hours</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label
                className="text-vital-lime text-lg font-semibold flex items-center gap-2"
                onMouseEnter={() => showTooltip("Your diet accounts for 80% of your results!")}
              >
                <Salad size={18} />
                Dietary Preferences
              </Label>
              <Select onValueChange={(value) => handleInputChange('dietaryRestrictions', value)}>
                <SelectTrigger className="bg-vital-gray-medium border-vital-lime/30 text-white mt-2 focus:ring-vital-lime focus:border-vital-lime focus:ring-opacity-50 transition-all">
                  <SelectValue placeholder="e.g., Vegan" />
                </SelectTrigger>
                <SelectContent className="bg-vital-gray-dark border-vital-lime/30">
                  <SelectItem value="none">No restrictions</SelectItem>
                  <SelectItem value="vegetarian">Vegetarian</SelectItem>
                  <SelectItem value="vegan">Vegan</SelectItem>
                  <SelectItem value="keto">Keto</SelectItem>
                  <SelectItem value="paleo">Paleo</SelectItem>
                  <SelectItem value="gluten-free">Gluten-free</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6 animate-fade-in">
            <div>
              <Label
                className="text-vital-lime text-lg font-semibold flex items-center gap-2"
                onMouseEnter={() => showTooltip("Hydration impacts your energy levels and recovery!")}
              >
                <Zap size={18} />
                Daily Water Intake Goal (Liters)
              </Label>
              <Select onValueChange={(value) => handleInputChange('waterIntake', value)}>
                <SelectTrigger className="bg-vital-gray-medium border-vital-lime/30 text-white mt-2 focus:ring-vital-lime focus:border-vital-lime focus:ring-opacity-50 transition-all">
                  <SelectValue placeholder="e.g., 3 Liters" />
                </SelectTrigger>
                <SelectContent className="bg-vital-gray-dark border-vital-lime/30">
                  <SelectItem value="2">2 Liters</SelectItem>
                  <SelectItem value="2.5">2.5 Liters</SelectItem>
                  <SelectItem value="3">3 Liters</SelectItem>
                  <SelectItem value="3.5">3.5 Liters</SelectItem>
                  <SelectItem value="4">4 Liters</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label
                className="text-vital-lime text-lg font-semibold flex items-center gap-2"
                onMouseEnter={() => showTooltip("Sleep is when your body rebuilds and recovers!")}
              >
                <Zap size={18} />
                Sleep Hours per Night
              </Label>
              <Select onValueChange={(value) => handleInputChange('sleepHours', value)}>
                <SelectTrigger className="bg-vital-gray-medium border-vital-lime/30 text-white mt-2 focus:ring-vital-lime focus:border-vital-lime focus:ring-opacity-50 transition-all">
                  <SelectValue placeholder="e.g., 8 Hours" />
                </SelectTrigger>
                <SelectContent className="bg-vital-gray-dark border-vital-lime/30">
                  <SelectItem value="6">6 hours</SelectItem>
                  <SelectItem value="7">7 hours</SelectItem>
                  <SelectItem value="8">8 hours</SelectItem>
                  <SelectItem value="9">9 hours</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label
                className="text-vital-lime text-lg font-semibold flex items-center gap-2"
                onMouseEnter={() => showTooltip("Different bodies have different needs!")}
              >
                <Zap size={18} />
                Gender
              </Label>
              <Select onValueChange={(value) => handleInputChange('gender', value)}>
                <SelectTrigger className="bg-vital-gray-medium border-vital-lime/30 text-white mt-2 focus:ring-vital-lime focus:border-vital-lime focus:ring-opacity-50 transition-all">
                  <SelectValue placeholder="Select gender" />
                </SelectTrigger>
                <SelectContent className="bg-vital-gray-dark border-vital-lime/30">
                  <SelectItem value="male">Male</SelectItem>
                  <SelectItem value="female">Female</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6 animate-fade-in">
            <div className="grid grid-cols-3 gap-4">
              <div>
                <Label
                  htmlFor="age"
                  className="text-vital-lime text-lg font-semibold flex items-center gap-2"
                  onMouseEnter={() => showTooltip("Your age helps us tailor intensity levels.")}
                >
                  <Zap size={18} />
                  Age
                </Label>
                <Input
                  id="age"
                  type="number"
                  placeholder="25"
                  value={formData.age}
                  className="bg-vital-gray-medium border-vital-lime/30 text-white mt-2 focus:ring-vital-lime focus:border-vital-lime focus:ring-opacity-50 transition-all"
                  onChange={(e) => handleInputChange('age', e.target.value)}
                />
              </div>

              <div>
                <Label
                  htmlFor="height"
                  className="text-vital-lime text-lg font-semibold flex items-center gap-2"
                  onMouseEnter={() => showTooltip("Height is used to calculate your ideal macros.")}
                >
                  <Zap size={18} />
                  Height (cm)
                </Label>
                <Input
                  id="height"
                  type="number"
                  placeholder="175"
                  value={formData.height}
                  className="bg-vital-gray-medium border-vital-lime/30 text-white mt-2 focus:ring-vital-lime focus:border-vital-lime focus:ring-opacity-50 transition-all"
                  onChange={(e) => handleInputChange('height', e.target.value)}
                />
              </div>

              <div>
                <Label
                  htmlFor="weight"
                  className="text-vital-lime text-lg font-semibold flex items-center gap-2"
                  onMouseEnter={() => showTooltip("Your current weight helps set realistic goals.")}
                >
                  <Zap size={18} />
                  Weight (kg)
                </Label>
                <Input
                  id="weight"
                  type="number"
                  placeholder="70"
                  value={formData.weight}
                  className="bg-vital-gray-medium border-vital-lime/30 text-white mt-2 focus:ring-vital-lime focus:border-vital-lime focus:ring-opacity-50 transition-all"
                  onChange={(e) => handleInputChange('weight', e.target.value)}
                />
              </div>
            </div>

            <div className="bg-vital-gray-medium/50 p-6 rounded-lg border border-vital-lime/20 backdrop-blur-sm">
              <h3 className="text-vital-lime font-semibold mb-3 flex items-center gap-2"><Zap size={16} /> Pro Tip</h3>
              <p className="text-gray-300 text-sm">
                Accurate measurements help us create the most effective plan for your body composition and metabolic needs.
              </p>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-black text-white p-6 relative overflow-hidden">
      {/* Gradient effect background */}
      <div className="absolute inset-0 bg-gradient-radial from-vital-gray-dark to-black opacity-70 z-0"></div>
      <div className="absolute top-0 left-0 w-full h-64 bg-gradient-to-r from-vital-lime/10 to-vital-lime-bright/5 blur-3xl opacity-20 z-0"></div>
      <div className="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-l from-vital-lime/10 to-vital-lime-bright/5 blur-3xl opacity-20 z-0"></div>

      {/* Content */}
      <div className="container mx-auto max-w-2xl relative z-10">
        <div className="flex items-center justify-between mb-8">
          <Button
            variant="ghost"
            onClick={handleBack}
            className="text-vital-lime hover:bg-vital-lime/10"
          >
            <ArrowUp className="h-4 w-4 mr-2 rotate-[-90deg]" />
            Back
          </Button>
          <div className="text-2xl font-bold text-vital-lime">VitalEdge</div>
          <div className="text-sm text-gray-400">Step {currentStep} of {totalSteps}: {getStepName()}</div>
        </div>

        {/* Progress Bar */}
        <div className="mb-8">
          <Progress value={progress} className="h-3 bg-vital-gray-dark">
            <div
              className="h-full bg-gradient-to-r from-vital-lime to-vital-lime-bright transition-all duration-500 ease-out shadow-[0_0_8px_rgba(50,205,50,0.6)]"
              style={{ width: `${progress}%` }}
            />
          </Progress>
        </div>

        {/* Form Card */}
        <Card className="bg-vital-gray-dark/80 border-vital-lime/20 animate-scale-in backdrop-blur-md relative overflow-hidden shadow-lg">
          <div className="absolute inset-0 border border-vital-lime/10 rounded-lg pointer-events-none"></div>
          <CardHeader>
            <CardTitle className="text-2xl text-vital-lime text-center flex flex-col gap-2">
              {currentStep === 1 && "Let's Set Your Goals"}
              {currentStep === 2 && "Lifestyle Preferences"}
              {currentStep === 3 && "Body Metrics"}
              <span className="text-sm font-normal text-vital-lime/70 animate-fade-in">
                {currentStep === 1 && "Your journey starts here. Define your limits – and break them."}
                {currentStep === 2 && "Small daily habits lead to transformative results."}
                {currentStep === 3 && "Precision leads to personalized perfection."}
              </span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {renderStep()}

            <div className="flex gap-4 pt-6">
              <Button
                onClick={handleNext}
                className="flex-1 bg-gradient-to-r from-vital-lime to-vital-lime-bright text-black hover:from-vital-lime-bright hover:to-vital-lime font-bold py-3 transition-all duration-300 hover:scale-105 relative overflow-hidden group"
                onMouseEnter={() => setIsHovering(true)}
                onMouseLeave={() => setIsHovering(false)}
              >
                <span className="absolute inset-0 w-full h-full bg-white/20 transform scale-x-0 origin-right transition-transform group-hover:scale-x-100 group-hover:origin-left"></span>
                <span className="relative flex items-center justify-center gap-2">
                  {currentStep === totalSteps ? 'Generate My Plan' : 'Next Step'}
                  <ArrowRight className={`h-4 w-4 transition-transform duration-300 ${isHovering ? 'translate-x-1' : ''}`} />
                </span>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Motivational Quote */}
        <div className="text-center mt-8 animate-fade-in">
          <p className="text-gray-400">
            {quote && `"${quote}"`}
          </p>
        </div>
      </div>
    </div>
  );
};

export default GeneratePlan;
